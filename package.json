{"name": "concure-clinic-management", "version": "1.0.0", "description": "ConCure Clinic Management System - Frontend Assets", "private": true, "scripts": {"dev": "vite", "build": "vite build", "watch": "vite build --watch", "hot": "vite --host", "preview": "vite preview"}, "devDependencies": {"@popperjs/core": "^2.11.8", "axios": "^1.1.2", "bootstrap": "^5.3.0", "laravel-vite-plugin": "^0.8.0", "sass": "^1.56.1", "vite": "^4.0.0"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.0", "chart.js": "^4.3.0", "sweetalert2": "^11.7.0"}, "keywords": ["clinic", "management", "healthcare", "laravel", "bootstrap", "medical"], "author": "Connect Pure", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-repo/concure-clinic"}, "bugs": {"url": "https://github.com/your-repo/concure-clinic/issues"}, "homepage": "https://github.com/your-repo/concure-clinic#readme"}