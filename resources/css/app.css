/* ConCure Clinic Management System - Main Stylesheet */

/* Import Bootstrap */
@import 'bootstrap/scss/bootstrap';

/* Import Font Awesome */
@import '@fortawesome/fontawesome-free/css/all.css';

/* ConCure Custom Variables */
:root {
    --concure-primary: #008080;
    --concure-primary-dark: #006666;
    --concure-primary-light: #4db8b8;
    --concure-secondary: #f8f9fa;
    --concure-success: #28a745;
    --concure-danger: #dc3545;
    --concure-warning: #ffc107;
    --concure-info: #17a2b8;
    --concure-light: #f8f9fa;
    --concure-dark: #343a40;
    
    /* Spacing */
    --concure-border-radius: 8px;
    --concure-box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    --concure-box-shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.12);
    
    /* Typography */
    --concure-font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --concure-font-size-base: 1rem;
    --concure-line-height-base: 1.6;
}

/* Base Styles */
body {
    font-family: var(--concure-font-family);
    font-size: var(--concure-font-size-base);
    line-height: var(--concure-line-height-base);
    background-color: var(--concure-secondary);
    color: var(--concure-dark);
}

/* Override Bootstrap Primary Color */
.btn-primary {
    background-color: var(--concure-primary);
    border-color: var(--concure-primary);
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: var(--concure-primary-dark);
    border-color: var(--concure-primary-dark);
}

.text-primary {
    color: var(--concure-primary) !important;
}

.bg-primary {
    background-color: var(--concure-primary) !important;
}

.border-primary {
    border-color: var(--concure-primary) !important;
}

/* Navigation */
.navbar-brand {
    font-weight: 600;
    color: var(--concure-primary) !important;
    font-size: 1.5rem;
}

.navbar-brand:hover {
    color: var(--concure-primary-dark) !important;
}

/* Sidebar */
.sidebar {
    min-height: calc(100vh - 56px);
    background: linear-gradient(135deg, var(--concure-primary) 0%, var(--concure-primary-dark) 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    border-radius: var(--concure-border-radius);
    margin: 2px 10px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
}

/* Cards */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: var(--concure-box-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--concure-box-shadow-hover);
}

.card-header {
    background-color: rgba(0, 128, 128, 0.05);
    border-bottom: 1px solid rgba(0, 128, 128, 0.1);
    border-radius: 12px 12px 0 0 !important;
    font-weight: 600;
}

.card-title {
    color: var(--concure-primary);
    font-weight: 600;
}

/* Forms */
.form-control:focus,
.form-select:focus {
    border-color: var(--concure-primary-light);
    box-shadow: 0 0 0 0.2rem rgba(0, 128, 128, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--concure-dark);
    margin-bottom: 0.5rem;
}

.input-group-text {
    background-color: var(--concure-light);
    border-color: #ced4da;
}

/* Tables */
.table {
    border-radius: var(--concure-border-radius);
    overflow: hidden;
    box-shadow: var(--concure-box-shadow);
}

.table thead th {
    background-color: var(--concure-primary);
    color: white;
    border: none;
    font-weight: 500;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table tbody tr:hover {
    background-color: rgba(0, 128, 128, 0.05);
}

/* Badges */
.badge {
    font-size: 0.75em;
    padding: 0.5em 0.75em;
    border-radius: 6px;
    font-weight: 500;
}

/* Buttons */
.btn {
    border-radius: var(--concure-border-radius);
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn {
    transform: none;
}

.btn-group .btn:hover {
    transform: none;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--concure-border-radius);
    box-shadow: var(--concure-box-shadow);
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left: 4px solid var(--concure-success);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left: 4px solid var(--concure-danger);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left: 4px solid var(--concure-warning);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: #0c5460;
    border-left: 4px solid var(--concure-info);
}

/* Language Switcher */
.language-switcher .dropdown-toggle {
    border: none;
    background: transparent;
    color: var(--concure-primary);
    font-weight: 500;
}

.language-switcher .dropdown-menu {
    border-radius: var(--concure-border-radius);
    border: 1px solid rgba(0, 128, 128, 0.2);
    box-shadow: var(--concure-box-shadow);
}

.language-switcher .dropdown-item:hover {
    background-color: rgba(0, 128, 128, 0.1);
    color: var(--concure-primary);
}

/* File Upload */
.file-preview {
    margin-top: 1rem;
    padding: 1rem;
    border: 2px dashed #dee2e6;
    border-radius: var(--concure-border-radius);
    text-align: center;
    background-color: #f8f9fa;
}

.file-preview.has-file {
    border-color: var(--concure-primary);
    background-color: rgba(0, 128, 128, 0.05);
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* RTL Support */
[dir="rtl"] .sidebar .nav-link:hover,
[dir="rtl"] .sidebar .nav-link.active {
    transform: translateX(-5px);
}

[dir="rtl"] .sidebar .nav-link i {
    margin-right: 0;
    margin-left: 10px;
}

[dir="rtl"] .dropdown-menu {
    right: 0;
    left: auto;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: var(--concure-border-radius);
    }
    
    .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-group-vertical .btn {
        margin-bottom: 0.25rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .language-switcher {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    .table {
        box-shadow: none;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--concure-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--concure-primary-dark);
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-muted-light {
    color: #8e9aaf !important;
}

.bg-light-primary {
    background-color: rgba(0, 128, 128, 0.1) !important;
}

.border-light-primary {
    border-color: rgba(0, 128, 128, 0.3) !important;
}

.shadow-sm-custom {
    box-shadow: var(--concure-box-shadow) !important;
}

.shadow-custom {
    box-shadow: var(--concure-box-shadow-hover) !important;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background-color: var(--concure-success);
}

.status-indicator.inactive {
    background-color: var(--concure-danger);
}

.status-indicator.pending {
    background-color: var(--concure-warning);
}

/* Dashboard Widgets */
.dashboard-widget {
    background: linear-gradient(135deg, var(--concure-primary) 0%, var(--concure-primary-dark) 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.dashboard-widget::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.dashboard-widget .widget-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.dashboard-widget .widget-value {
    font-size: 2rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.dashboard-widget .widget-label {
    font-size: 0.875rem;
    opacity: 0.9;
}
