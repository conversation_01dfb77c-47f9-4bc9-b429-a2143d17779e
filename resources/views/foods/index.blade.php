@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-apple-alt text-primary"></i>
                    Food Composition Database
                </h1>
                @can('manage-food-composition')
                <div>
                    <a href="{{ route('food-groups.index') }}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-layer-group"></i> Food Groups
                    </a>
                    <a href="{{ route('foods.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Food
                    </a>
                </div>
                @endcan
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" action="{{ route('foods.index') }}">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="search" class="form-label">Search Foods</label>
                                <input type="text" class="form-control" id="search" name="search" 
                                       value="{{ request('search') }}" placeholder="Search by name...">
                            </div>
                            <div class="col-md-3">
                                <label for="food_group_id" class="form-label">Food Group</label>
                                <select class="form-select" id="food_group_id" name="food_group_id">
                                    <option value="">All Groups</option>
                                    @foreach($foodGroups as $group)
                                        <option value="{{ $group->id }}" 
                                                {{ request('food_group_id') == $group->id ? 'selected' : '' }}>
                                            {{ $group->translated_name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="type" class="form-label">Type</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="">All Types</option>
                                    <option value="standard" {{ request('type') === 'standard' ? 'selected' : '' }}>
                                        Standard
                                    </option>
                                    <option value="custom" {{ request('type') === 'custom' ? 'selected' : '' }}>
                                        Custom
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="nutrition_filter" class="form-label">Nutrition</label>
                                <select class="form-select" id="nutrition_filter" name="nutrition_filter">
                                    <option value="">All Foods</option>
                                    <option value="high_protein" {{ request('nutrition_filter') === 'high_protein' ? 'selected' : '' }}>
                                        High Protein
                                    </option>
                                    <option value="low_calorie" {{ request('nutrition_filter') === 'low_calorie' ? 'selected' : '' }}>
                                        Low Calorie
                                    </option>
                                    <option value="high_fiber" {{ request('nutrition_filter') === 'high_fiber' ? 'selected' : '' }}>
                                        High Fiber
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Foods Grid -->
    <div class="row">
        @forelse($foods as $food)
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ $food->translated_name }}</h6>
                    <div>
                        @if($food->is_custom)
                            <span class="badge bg-info">Custom</span>
                        @else
                            <span class="badge bg-secondary">Standard</span>
                        @endif
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <span class="badge" style="background-color: {{ $food->foodGroup->color }}">
                            {{ $food->foodGroup->translated_name }}
                        </span>
                    </div>
                    
                    @if($food->translated_description)
                    <p class="text-muted small mb-3">{{ Str::limit($food->translated_description, 100) }}</p>
                    @endif

                    <!-- Nutrition Summary (per 100g) -->
                    <div class="row text-center mb-3">
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <div class="fw-bold text-primary">{{ $food->calories }}</div>
                                <small class="text-muted">kcal</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <div class="fw-bold text-success">{{ $food->protein }}g</div>
                                <small class="text-muted">protein</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <div class="fw-bold text-warning">{{ $food->carbohydrates }}g</div>
                                <small class="text-muted">carbs</small>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <div class="fw-bold text-danger">{{ $food->fat }}g</div>
                                <small class="text-muted">fat</small>
                            </div>
                        </div>
                    </div>

                    @if($food->serving_size)
                    <p class="small text-muted mb-2">
                        <i class="fas fa-utensils"></i>
                        Serving: {{ $food->serving_size }}
                        @if($food->serving_weight)
                            ({{ $food->serving_weight }}g)
                        @endif
                    </p>
                    @endif
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('foods.show', $food) }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                        @can('manage-food-composition')
                            @if($food->is_custom || auth()->user()->role === 'program_owner')
                            <div class="btn-group">
                                <a href="{{ route('foods.edit', $food) }}" class="btn btn-sm btn-outline-secondary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="confirmDelete('{{ $food->id }}', '{{ $food->translated_name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            @endif
                        @endcan
                    </div>
                </div>
            </div>
        </div>
        @empty
        <div class="col-12">
            <div class="text-center py-5">
                <i class="fas fa-apple-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No foods found</h5>
                <p class="text-muted">Try adjusting your search criteria or add a new food item.</p>
                @can('manage-food-composition')
                <a href="{{ route('foods.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add First Food
                </a>
                @endcan
            </div>
        </div>
        @endforelse
    </div>

    <!-- Pagination -->
    @if($foods->hasPages())
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                {{ $foods->withQueryString()->links() }}
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Deletion</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="foodName"></strong>?</p>
                <p class="text-muted small">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function confirmDelete(foodId, foodName) {
    document.getElementById('foodName').textContent = foodName;
    document.getElementById('deleteForm').action = `/foods/${foodId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
@endpush
@endsection
