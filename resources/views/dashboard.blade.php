@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-tachometer-alt text-primary"></i>
                        Dashboard
                    </h1>
                    <p class="text-muted mb-0">Welcome back, {{ auth()->user()->full_name }}!</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">{{ now()->format('l, F j, Y') }}</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        @if(isset($totalPatients))
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Total Patients</h6>
                            <h2 class="mb-0">{{ number_format($totalPatients) }}</h2>
                            @if(isset($newPatientsThisMonth) && $newPatientsThisMonth > 0)
                            <small>+{{ $newPatientsThisMonth }} this month</small>
                            @endif
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(isset($activePrescriptions))
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Active Prescriptions</h6>
                            <h2 class="mb-0">{{ number_format($activePrescriptions) }}</h2>
                            @if(isset($prescriptionsThisMonth) && $prescriptionsThisMonth > 0)
                            <small>{{ $prescriptionsThisMonth }} this month</small>
                            @endif
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-prescription-bottle-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(isset($pendingLabRequests))
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Pending Lab Requests</h6>
                            <h2 class="mb-0">{{ number_format($pendingLabRequests) }}</h2>
                            @if(isset($urgentLabRequests) && $urgentLabRequests > 0)
                            <small>{{ $urgentLabRequests }} urgent</small>
                            @endif
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-flask fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(isset($totalRevenue))
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">Monthly Revenue</h6>
                            <h2 class="mb-0">${{ number_format($totalRevenue, 2) }}</h2>
                            @if(isset($pendingInvoices) && $pendingInvoices > 0)
                            <small>{{ $pendingInvoices }} pending invoices</small>
                            @endif
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @can('manage-patients')
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ route('patients.create') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-user-plus d-block mb-1"></i>
                                <small>Add Patient</small>
                            </a>
                        </div>
                        @endcan

                        @can('create-prescriptions')
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ route('recommendations.prescriptions') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-prescription d-block mb-1"></i>
                                <small>New Prescription</small>
                            </a>
                        </div>
                        @endcan

                        @can('create-prescriptions')
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ route('recommendations.lab-requests') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-vial d-block mb-1"></i>
                                <small>Lab Request</small>
                            </a>
                        </div>
                        @endcan

                        @can('manage-finance')
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ route('finance.invoices') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-file-invoice d-block mb-1"></i>
                                <small>New Invoice</small>
                            </a>
                        </div>
                        @endcan

                        @can('manage-users')
                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ route('users.create') }}" class="btn btn-outline-secondary w-100">
                                <i class="fas fa-user-cog d-block mb-1"></i>
                                <small>Add User</small>
                            </a>
                        </div>
                        @endcan

                        <div class="col-lg-2 col-md-4 col-6 mb-3">
                            <a href="{{ route('settings.index') }}" class="btn btn-outline-dark w-100">
                                <i class="fas fa-cog d-block mb-1"></i>
                                <small>Settings</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Recent Activity -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-history"></i>
                        Recent Activity
                    </h6>
                    @can('view-audit-logs')
                    <a href="{{ route('settings.audit-logs') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                    @endcan
                </div>
                <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                    @if(isset($recentActivity) && count($recentActivity) > 0)
                        @foreach($recentActivity as $activity)
                        <div class="border-bottom p-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0 me-3">
                                    <div class="bg-light rounded-circle p-2" style="width: 40px; height: 40px;">
                                        <i class="fas fa-user text-muted"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">{{ $activity['description'] }}</h6>
                                    <p class="mb-1 text-muted small">
                                        by {{ $activity['user_name'] ?? 'System' }}
                                    </p>
                                    <small class="text-muted">
                                        {{ \Carbon\Carbon::parse($activity['performed_at'])->diffForHumans() }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    @else
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-history fa-2x mb-2"></i>
                        <p class="mb-0">No recent activity</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Upcoming Appointments -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar-alt"></i>
                        Upcoming Appointments
                    </h6>
                    <a href="#" class="btn btn-sm btn-outline-primary">
                        View Calendar
                    </a>
                </div>
                <div class="card-body p-0" style="max-height: 400px; overflow-y: auto;">
                    @if(isset($upcomingAppointments) && count($upcomingAppointments) > 0)
                        @foreach($upcomingAppointments as $appointment)
                        <div class="border-bottom p-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ $appointment['patient']['full_name'] ?? 'Unknown Patient' }}</h6>
                                    <p class="mb-1 text-muted small">
                                        with Dr. {{ $appointment['doctor']['full_name'] ?? 'Unknown Doctor' }}
                                    </p>
                                    <small class="text-muted">
                                        {{ \Carbon\Carbon::parse($appointment['appointment_datetime'])->format('M d, Y g:i A') }}
                                    </small>
                                </div>
                                <span class="badge bg-primary">
                                    {{ ucfirst($appointment['type'] ?? 'consultation') }}
                                </span>
                            </div>
                        </div>
                        @endforeach
                    @else
                    <div class="p-4 text-center text-muted">
                        <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                        <p class="mb-0">No upcoming appointments</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    @if(isset($monthlyStats) && count($monthlyStats) > 0)
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-chart-line"></i>
                        Monthly Trends
                    </h6>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

@push('scripts')
@if(isset($monthlyStats) && count($monthlyStats) > 0)
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('monthlyChart').getContext('2d');
    const monthlyStats = @json($monthlyStats);
    
    const labels = Object.keys(monthlyStats);
    const patientsData = Object.values(monthlyStats).map(stat => stat.patients);
    const prescriptionsData = Object.values(monthlyStats).map(stat => stat.prescriptions);
    const revenueData = Object.values(monthlyStats).map(stat => stat.revenue);
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'New Patients',
                    data: patientsData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Prescriptions',
                    data: prescriptionsData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1
                },
                {
                    label: 'Revenue ($)',
                    data: revenueData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.1)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
});
</script>
@endif
@endpush
@endsection
