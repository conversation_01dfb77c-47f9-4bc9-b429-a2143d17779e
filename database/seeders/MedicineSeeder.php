<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MedicineSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the demo clinic ID and admin user ID
        $clinic = DB::table('clinics')->where('name', 'Demo Clinic')->first();
        $admin = DB::table('users')->where('role', 'admin')->first();

        $medicines = [
            [
                'name' => 'Paracetamol',
                'generic_name' => 'Acetaminophen',
                'brand_name' => 'Tylenol',
                'dosage' => '500mg',
                'form' => 'Tablet',
                'description' => 'Pain reliever and fever reducer',
                'side_effects' => 'Nausea, stomach pain, loss of appetite',
                'contraindications' => 'Severe liver disease',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Ibuprofen',
                'generic_name' => 'Ibuprofen',
                'brand_name' => 'Advil',
                'dosage' => '400mg',
                'form' => 'Tablet',
                'description' => 'Anti-inflammatory pain reliever',
                'side_effects' => 'Stomach upset, dizziness, headache',
                'contraindications' => 'Peptic ulcer, severe heart failure',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Amoxicillin',
                'generic_name' => 'Amoxicillin',
                'brand_name' => 'Amoxil',
                'dosage' => '500mg',
                'form' => 'Capsule',
                'description' => 'Antibiotic for bacterial infections',
                'side_effects' => 'Diarrhea, nausea, skin rash',
                'contraindications' => 'Penicillin allergy',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Metformin',
                'generic_name' => 'Metformin HCl',
                'brand_name' => 'Glucophage',
                'dosage' => '500mg',
                'form' => 'Tablet',
                'description' => 'Diabetes medication to control blood sugar',
                'side_effects' => 'Nausea, diarrhea, metallic taste',
                'contraindications' => 'Kidney disease, liver disease',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Lisinopril',
                'generic_name' => 'Lisinopril',
                'brand_name' => 'Prinivil',
                'dosage' => '10mg',
                'form' => 'Tablet',
                'description' => 'ACE inhibitor for high blood pressure',
                'side_effects' => 'Dry cough, dizziness, fatigue',
                'contraindications' => 'Pregnancy, angioedema history',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Omeprazole',
                'generic_name' => 'Omeprazole',
                'brand_name' => 'Prilosec',
                'dosage' => '20mg',
                'form' => 'Capsule',
                'description' => 'Proton pump inhibitor for acid reflux',
                'side_effects' => 'Headache, nausea, diarrhea',
                'contraindications' => 'Hypersensitivity to benzimidazoles',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Atorvastatin',
                'generic_name' => 'Atorvastatin Calcium',
                'brand_name' => 'Lipitor',
                'dosage' => '20mg',
                'form' => 'Tablet',
                'description' => 'Statin for cholesterol management',
                'side_effects' => 'Muscle pain, liver problems, digestive issues',
                'contraindications' => 'Active liver disease, pregnancy',
                'is_frequent' => false,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Levothyroxine',
                'generic_name' => 'Levothyroxine Sodium',
                'brand_name' => 'Synthroid',
                'dosage' => '50mcg',
                'form' => 'Tablet',
                'description' => 'Thyroid hormone replacement',
                'side_effects' => 'Heart palpitations, nervousness, insomnia',
                'contraindications' => 'Untreated adrenal insufficiency',
                'is_frequent' => false,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Aspirin',
                'generic_name' => 'Acetylsalicylic Acid',
                'brand_name' => 'Bayer',
                'dosage' => '81mg',
                'form' => 'Tablet',
                'description' => 'Low-dose aspirin for heart protection',
                'side_effects' => 'Stomach irritation, bleeding risk',
                'contraindications' => 'Bleeding disorders, children with viral infections',
                'is_frequent' => true,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Vitamin D3',
                'generic_name' => 'Cholecalciferol',
                'brand_name' => 'Various',
                'dosage' => '1000 IU',
                'form' => 'Capsule',
                'description' => 'Vitamin D supplement',
                'side_effects' => 'Nausea, vomiting, weakness (with overdose)',
                'contraindications' => 'Hypercalcemia, kidney stones',
                'is_frequent' => false,
                'clinic_id' => $clinic->id,
                'created_by' => $admin->id,
                'is_active' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('medicines')->insert($medicines);
    }
}
