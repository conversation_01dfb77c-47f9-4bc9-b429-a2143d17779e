<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'username',
        'email',
        'password',
        'first_name',
        'last_name',
        'phone',
        'role',
        'is_active',
        'activation_code',
        'activated_at',
        'language',
        'permissions',
        'clinic_id',
        'created_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'activated_at' => 'datetime',
        'permissions' => 'array',
        'is_active' => 'boolean',
        'password' => 'hashed',
    ];

    /**
     * User roles
     */
    const ROLES = [
        'program_owner' => 'Program Owner',
        'admin' => 'Admin',
        'doctor' => 'Doctor',
        'assistant' => 'Assistant',
        'nurse' => 'Nurse',
        'accountant' => 'Accountant',
        'patient' => 'Patient',
    ];

    /**
     * Get the clinic that owns the user.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the user who created this user.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the users created by this user.
     */
    public function createdUsers(): HasMany
    {
        return $this->hasMany(User::class, 'created_by');
    }

    /**
     * Get the patients created by this user.
     */
    public function patients(): HasMany
    {
        return $this->hasMany(Patient::class, 'created_by');
    }

    /**
     * Get the audit logs for this user.
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    /**
     * Get the activation codes created by this user.
     */
    public function createdActivationCodes(): HasMany
    {
        return $this->hasMany(ActivationCode::class, 'created_by');
    }

    /**
     * Get the activation codes used by this user.
     */
    public function usedActivationCodes(): HasMany
    {
        return $this->hasMany(ActivationCode::class, 'used_by');
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if user has any of the specified roles.
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles);
    }

    /**
     * Check if user is active and activated.
     */
    public function isActiveAndActivated(): bool
    {
        return $this->is_active && $this->activated_at !== null;
    }

    /**
     * Get user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->first_name . ' ' . $this->last_name;
    }

    /**
     * Get user's role display name.
     */
    public function getRoleDisplayAttribute(): string
    {
        return self::ROLES[$this->role] ?? $this->role;
    }

    /**
     * Check if user can manage other users.
     */
    public function canManageUsers(): bool
    {
        return in_array($this->role, ['program_owner', 'admin']);
    }

    /**
     * Check if user can access financial data.
     */
    public function canAccessFinance(): bool
    {
        return in_array($this->role, ['admin', 'accountant']);
    }

    /**
     * Check if user can manage patients.
     */
    public function canManagePatients(): bool
    {
        return in_array($this->role, ['admin', 'doctor', 'assistant', 'nurse']);
    }

    /**
     * Check if user can create prescriptions.
     */
    public function canPrescribe(): bool
    {
        return in_array($this->role, ['doctor']);
    }

    /**
     * Scope to filter by role.
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope to filter active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter activated users.
     */
    public function scopeActivated($query)
    {
        return $query->whereNotNull('activated_at');
    }

    /**
     * Scope to filter by clinic.
     */
    public function scopeByClinic($query, int $clinicId)
    {
        return $query->where('clinic_id', $clinicId);
    }
}
