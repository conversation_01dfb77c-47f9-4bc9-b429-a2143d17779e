<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Clinic extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'logo',
        'settings',
        'is_active',
        'max_users',
        'activation_code',
        'activated_at',
        'subscription_expires_at',
    ];

    protected $casts = [
        'settings' => 'array',
        'is_active' => 'boolean',
        'activated_at' => 'datetime',
        'subscription_expires_at' => 'date',
    ];

    /**
     * Get the users for the clinic.
     */
    public function users(): Has<PERSON>any
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the patients for the clinic.
     */
    public function patients(): HasMany
    {
        return $this->hasMany(Patient::class);
    }

    /**
     * Get the medicines for the clinic.
     */
    public function medicines(): Has<PERSON>any
    {
        return $this->hasMany(Medicine::class);
    }

    /**
     * Get the lab tests for the clinic.
     */
    public function labTests(): Has<PERSON>any
    {
        return $this->hasMany(LabTest::class);
    }

    /**
     * Get the invoices for the clinic.
     */
    public function invoices(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Get the expenses for the clinic.
     */
    public function expenses(): HasMany
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * Get the advertisements for the clinic.
     */
    public function advertisements(): HasMany
    {
        return $this->hasMany(Advertisement::class);
    }

    /**
     * Get the audit logs for the clinic.
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }

    /**
     * Get the activation codes for the clinic.
     */
    public function activationCodes(): HasMany
    {
        return $this->hasMany(ActivationCode::class);
    }

    /**
     * Get the settings for the clinic.
     */
    public function clinicSettings(): HasMany
    {
        return $this->hasMany(Setting::class);
    }

    /**
     * Get the appointments for the clinic.
     */
    public function appointments(): HasMany
    {
        return $this->hasMany(Appointment::class);
    }

    /**
     * Get the communication logs for the clinic.
     */
    public function communicationLogs(): HasMany
    {
        return $this->hasMany(CommunicationLog::class);
    }

    /**
     * Check if clinic is active and subscription is valid.
     */
    public function isActiveWithValidSubscription(): bool
    {
        return $this->is_active && 
               $this->activated_at !== null && 
               ($this->subscription_expires_at === null || $this->subscription_expires_at->isFuture());
    }

    /**
     * Check if clinic has reached user limit.
     */
    public function hasReachedUserLimit(): bool
    {
        return $this->users()->active()->count() >= $this->max_users;
    }

    /**
     * Get clinic setting value.
     */
    public function getSetting(string $key, $default = null)
    {
        return $this->settings[$key] ?? $default;
    }

    /**
     * Set clinic setting value.
     */
    public function setSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;
        $this->save();
    }

    /**
     * Scope to filter active clinics.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter activated clinics.
     */
    public function scopeActivated($query)
    {
        return $query->whereNotNull('activated_at');
    }

    /**
     * Scope to filter clinics with valid subscriptions.
     */
    public function scopeWithValidSubscription($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('subscription_expires_at')
              ->orWhere('subscription_expires_at', '>', now());
        });
    }
}
