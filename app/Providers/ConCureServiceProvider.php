<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Gate;
use App\Models\User;

class ConCureServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Share global data with all views
        View::composer('*', function ($view) {
            $view->with([
                'appName' => config('app.name'),
                'companyName' => config('app.concure.company_name'),
                'primaryColor' => config('app.concure.primary_color'),
                'supportedLanguages' => config('app.concure.supported_languages'),
            ]);
        });

        // Define authorization gates
        $this->defineGates();

        // Set locale based on session or user preference
        $this->setLocale();
    }

    /**
     * Define authorization gates.
     */
    private function defineGates(): void
    {
        // User management gates
        Gate::define('manage-users', function (User $user) {
            return in_array($user->role, ['program_owner', 'admin']);
        });

        Gate::define('manage-patients', function (User $user) {
            return in_array($user->role, ['admin', 'doctor', 'assistant', 'nurse']);
        });

        Gate::define('create-prescriptions', function (User $user) {
            return $user->role === 'doctor';
        });

        Gate::define('manage-finance', function (User $user) {
            return in_array($user->role, ['admin', 'accountant']);
        });

        Gate::define('manage-advertisements', function (User $user) {
            return in_array($user->role, ['program_owner', 'admin']);
        });

        Gate::define('view-audit-logs', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('manage-activation-codes', function (User $user) {
            return in_array($user->role, ['program_owner', 'admin']);
        });

        Gate::define('manage-food-composition', function (User $user) {
            return in_array($user->role, ['program_owner', 'admin', 'doctor']);
        });

        // Clinic-specific gates
        Gate::define('manage-clinic-settings', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('view-clinic-reports', function (User $user) {
            return in_array($user->role, ['admin', 'accountant']);
        });

        // Patient-specific gates
        Gate::define('view-patient', function (User $user, $patient) {
            if ($user->role === 'patient') {
                return $user->id === $patient->user_id; // If patient has user account
            }
            return in_array($user->role, ['admin', 'doctor', 'assistant', 'nurse']);
        });

        Gate::define('edit-patient', function (User $user, $patient) {
            return in_array($user->role, ['admin', 'doctor', 'assistant', 'nurse']);
        });

        Gate::define('delete-patient', function (User $user, $patient) {
            return in_array($user->role, ['admin', 'doctor']);
        });

        // Financial gates
        Gate::define('approve-discounts', function (User $user) {
            return $user->role === 'admin';
        });

        Gate::define('approve-expenses', function (User $user) {
            return $user->role === 'admin';
        });

        // Communication gates
        Gate::define('send-communications', function (User $user) {
            return in_array($user->role, ['admin', 'doctor', 'assistant']);
        });
    }

    /**
     * Set application locale.
     */
    private function setLocale(): void
    {
        // Check session for locale
        if (session()->has('locale')) {
            $locale = session('locale');
            if (in_array($locale, config('app.concure.supported_languages'))) {
                app()->setLocale($locale);
                return;
            }
        }

        // Check authenticated user's language preference
        if (auth()->check() && auth()->user()->language) {
            $locale = auth()->user()->language;
            if (in_array($locale, config('app.concure.supported_languages'))) {
                app()->setLocale($locale);
                session(['locale' => $locale]);
                return;
            }
        }

        // Fall back to default locale
        app()->setLocale(config('app.concure.default_language', 'en'));
    }
}
