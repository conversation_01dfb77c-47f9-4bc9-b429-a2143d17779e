<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Check if user has any of the required roles
        if (!$user->hasAnyRole($roles)) {
            abort(403, 'Access denied. Insufficient permissions.');
        }

        // Check if user is active and activated
        if (!$user->isActiveAndActivated()) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Your account is not active or not activated.');
        }

        // Check clinic status for non-program-owner users
        if ($user->role !== 'program_owner' && $user->clinic) {
            if (!$user->clinic->isActiveWithValidSubscription()) {
                auth()->logout();
                return redirect()->route('login')->with('error', 'Clinic subscription has expired or is inactive.');
            }
        }

        return $next($request);
    }
}
