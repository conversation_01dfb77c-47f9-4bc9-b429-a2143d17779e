<?php

namespace App\Http\Controllers;

use App\Models\Food;
use App\Models\FoodGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FoodController extends Controller
{
    /**
     * Display a listing of foods.
     */
    public function index(Request $request)
    {
        $query = Food::with(['foodGroup', 'clinic', 'creator']);

        // Apply filters
        if ($request->filled('food_group_id')) {
            $query->byFoodGroup($request->food_group_id);
        }

        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('type')) {
            if ($request->type === 'custom') {
                $query->custom();
            } elseif ($request->type === 'standard') {
                $query->standard();
            }
        }

        if ($request->filled('nutrition_filter')) {
            switch ($request->nutrition_filter) {
                case 'high_protein':
                    $query->highProtein();
                    break;
                case 'low_calorie':
                    $query->lowCalorie();
                    break;
                case 'high_fiber':
                    $query->highFiber();
                    break;
            }
        }

        $foods = $query->active()->orderBy('name')->paginate(20);
        $foodGroups = FoodGroup::active()->ordered()->get();

        return view('foods.index', compact('foods', 'foodGroups'));
    }

    /**
     * Show the form for creating a new food.
     */
    public function create()
    {
        $this->authorize('manage-food-composition');
        
        $foodGroups = FoodGroup::active()->ordered()->get();
        
        return view('foods.create', compact('foodGroups'));
    }

    /**
     * Store a newly created food.
     */
    public function store(Request $request)
    {
        $this->authorize('manage-food-composition');
        
        $user = auth()->user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_ku' => 'nullable|string|max:255',
            'food_group_id' => 'required|exists:food_groups,id',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_ku' => 'nullable|string',
            'calories' => 'required|numeric|min:0|max:9999',
            'protein' => 'required|numeric|min:0|max:999',
            'carbohydrates' => 'required|numeric|min:0|max:999',
            'fat' => 'required|numeric|min:0|max:999',
            'fiber' => 'nullable|numeric|min:0|max:999',
            'sugar' => 'nullable|numeric|min:0|max:999',
            'sodium' => 'nullable|numeric|min:0|max:99999',
            'potassium' => 'nullable|numeric|min:0|max:99999',
            'calcium' => 'nullable|numeric|min:0|max:99999',
            'iron' => 'nullable|numeric|min:0|max:999',
            'vitamin_c' => 'nullable|numeric|min:0|max:999',
            'vitamin_a' => 'nullable|numeric|min:0|max:99999',
            'serving_size' => 'nullable|string|max:255',
            'serving_weight' => 'nullable|numeric|min:0|max:9999',
        ]);

        // Prepare translations
        $nameTranslations = [];
        $descriptionTranslations = [];
        
        foreach (['en', 'ar', 'ku'] as $locale) {
            if ($request->filled("name_{$locale}")) {
                $nameTranslations[$locale] = $request->input("name_{$locale}");
            }
            if ($request->filled("description_{$locale}")) {
                $descriptionTranslations[$locale] = $request->input("description_{$locale}");
            }
        }

        $food = Food::create([
            'name' => $request->name,
            'name_translations' => $nameTranslations,
            'food_group_id' => $request->food_group_id,
            'description' => $request->description,
            'description_translations' => $descriptionTranslations,
            'calories' => $request->calories,
            'protein' => $request->protein,
            'carbohydrates' => $request->carbohydrates,
            'fat' => $request->fat,
            'fiber' => $request->fiber ?? 0,
            'sugar' => $request->sugar ?? 0,
            'sodium' => $request->sodium ?? 0,
            'potassium' => $request->potassium ?? 0,
            'calcium' => $request->calcium ?? 0,
            'iron' => $request->iron ?? 0,
            'vitamin_c' => $request->vitamin_c ?? 0,
            'vitamin_a' => $request->vitamin_a ?? 0,
            'serving_size' => $request->serving_size,
            'serving_weight' => $request->serving_weight,
            'is_custom' => true,
            'clinic_id' => $user->clinic_id,
            'created_by' => $user->id,
            'is_active' => true,
        ]);

        return redirect()->route('foods.show', $food)
                        ->with('success', 'Food item created successfully.');
    }

    /**
     * Display the specified food.
     */
    public function show(Food $food)
    {
        $food->load(['foodGroup', 'clinic', 'creator']);
        
        return view('foods.show', compact('food'));
    }

    /**
     * Show the form for editing the specified food.
     */
    public function edit(Food $food)
    {
        $this->authorize('manage-food-composition');
        
        // Only allow editing custom foods or if user is program owner
        if ($food->is_custom === false && auth()->user()->role !== 'program_owner') {
            abort(403, 'Cannot edit standard food items.');
        }
        
        $foodGroups = FoodGroup::active()->ordered()->get();
        
        return view('foods.edit', compact('food', 'foodGroups'));
    }

    /**
     * Update the specified food.
     */
    public function update(Request $request, Food $food)
    {
        $this->authorize('manage-food-composition');
        
        // Only allow editing custom foods or if user is program owner
        if ($food->is_custom === false && auth()->user()->role !== 'program_owner') {
            abort(403, 'Cannot edit standard food items.');
        }
        
        $request->validate([
            'name' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'name_ar' => 'nullable|string|max:255',
            'name_ku' => 'nullable|string|max:255',
            'food_group_id' => 'required|exists:food_groups,id',
            'description' => 'nullable|string',
            'description_en' => 'nullable|string',
            'description_ar' => 'nullable|string',
            'description_ku' => 'nullable|string',
            'calories' => 'required|numeric|min:0|max:9999',
            'protein' => 'required|numeric|min:0|max:999',
            'carbohydrates' => 'required|numeric|min:0|max:999',
            'fat' => 'required|numeric|min:0|max:999',
            'fiber' => 'nullable|numeric|min:0|max:999',
            'sugar' => 'nullable|numeric|min:0|max:999',
            'sodium' => 'nullable|numeric|min:0|max:99999',
            'potassium' => 'nullable|numeric|min:0|max:99999',
            'calcium' => 'nullable|numeric|min:0|max:99999',
            'iron' => 'nullable|numeric|min:0|max:999',
            'vitamin_c' => 'nullable|numeric|min:0|max:999',
            'vitamin_a' => 'nullable|numeric|min:0|max:99999',
            'serving_size' => 'nullable|string|max:255',
            'serving_weight' => 'nullable|numeric|min:0|max:9999',
            'is_active' => 'boolean',
        ]);

        // Prepare translations
        $nameTranslations = $food->name_translations ?? [];
        $descriptionTranslations = $food->description_translations ?? [];
        
        foreach (['en', 'ar', 'ku'] as $locale) {
            if ($request->filled("name_{$locale}")) {
                $nameTranslations[$locale] = $request->input("name_{$locale}");
            }
            if ($request->filled("description_{$locale}")) {
                $descriptionTranslations[$locale] = $request->input("description_{$locale}");
            }
        }

        $food->update([
            'name' => $request->name,
            'name_translations' => $nameTranslations,
            'food_group_id' => $request->food_group_id,
            'description' => $request->description,
            'description_translations' => $descriptionTranslations,
            'calories' => $request->calories,
            'protein' => $request->protein,
            'carbohydrates' => $request->carbohydrates,
            'fat' => $request->fat,
            'fiber' => $request->fiber ?? 0,
            'sugar' => $request->sugar ?? 0,
            'sodium' => $request->sodium ?? 0,
            'potassium' => $request->potassium ?? 0,
            'calcium' => $request->calcium ?? 0,
            'iron' => $request->iron ?? 0,
            'vitamin_c' => $request->vitamin_c ?? 0,
            'vitamin_a' => $request->vitamin_a ?? 0,
            'serving_size' => $request->serving_size,
            'serving_weight' => $request->serving_weight,
            'is_active' => $request->boolean('is_active', true),
        ]);

        return redirect()->route('foods.show', $food)
                        ->with('success', 'Food item updated successfully.');
    }

    /**
     * Remove the specified food.
     */
    public function destroy(Food $food)
    {
        $this->authorize('manage-food-composition');
        
        // Only allow deleting custom foods or if user is program owner
        if ($food->is_custom === false && auth()->user()->role !== 'program_owner') {
            abort(403, 'Cannot delete standard food items.');
        }

        // Check if food is used in any diet plans
        if ($food->dietPlanMealFoods()->count() > 0) {
            return back()->withErrors(['error' => 'Cannot delete food item that is used in diet plans.']);
        }

        $food->delete();

        return redirect()->route('foods.index')
                        ->with('success', 'Food item deleted successfully.');
    }

    /**
     * Calculate nutrition for a specific quantity.
     */
    public function calculateNutrition(Request $request, Food $food)
    {
        $request->validate([
            'quantity' => 'required|numeric|min:0.1',
            'unit' => 'required|string|in:g,kg,mg,cup,tbsp,tsp,serving',
        ]);

        $nutrition = $food->calculateNutrition($request->quantity, $request->unit);

        return response()->json([
            'success' => true,
            'nutrition' => $nutrition,
            'food' => [
                'name' => $food->translated_name,
                'quantity' => $request->quantity,
                'unit' => $request->unit,
            ]
        ]);
    }

    /**
     * Search foods for AJAX requests.
     */
    public function search(Request $request)
    {
        $request->validate([
            'q' => 'required|string|min:2',
            'limit' => 'nullable|integer|min:1|max:50',
        ]);

        $foods = Food::with('foodGroup')
                    ->active()
                    ->search($request->q)
                    ->limit($request->limit ?? 20)
                    ->get()
                    ->map(function ($food) {
                        return [
                            'id' => $food->id,
                            'name' => $food->translated_name,
                            'group' => $food->foodGroup->translated_name,
                            'calories' => $food->calories,
                            'protein' => $food->protein,
                            'carbohydrates' => $food->carbohydrates,
                            'fat' => $food->fat,
                        ];
                    });

        return response()->json($foods);
    }
}
