<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\PatientController;
use App\Http\Controllers\RecommendationController;
use App\Http\Controllers\FinanceController;
use App\Http\Controllers\AdvertisementController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\FoodCompositionController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Public routes
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication routes
Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('/login', [LoginController::class, 'login']);
Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
Route::post('/register', [RegisterController::class, 'register']);

// Language switching
Route::get('/language/{locale}', function ($locale) {
    if (in_array($locale, config('app.concure.supported_languages'))) {
        session(['locale' => $locale]);
    }
    return redirect()->back();
})->name('language.switch');

// Protected routes
Route::middleware(['auth', 'activation'])->group(function () {
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Patient Management
    Route::prefix('patients')->name('patients.')->group(function () {
        Route::get('/', [PatientController::class, 'index'])->name('index');
        Route::get('/create', [PatientController::class, 'create'])->name('create');
        Route::post('/', [PatientController::class, 'store'])->name('store');
        Route::get('/{patient}', [PatientController::class, 'show'])->name('show');
        Route::get('/{patient}/edit', [PatientController::class, 'edit'])->name('edit');
        Route::put('/{patient}', [PatientController::class, 'update'])->name('update');
        Route::delete('/{patient}', [PatientController::class, 'destroy'])->name('destroy');
        
        // Patient specific routes
        Route::get('/{patient}/history', [PatientController::class, 'history'])->name('history');
        Route::post('/{patient}/checkup', [PatientController::class, 'addCheckup'])->name('checkup');
        Route::post('/{patient}/upload', [PatientController::class, 'uploadFile'])->name('upload');
    });
    
    // Recommendations
    Route::prefix('recommendations')->name('recommendations.')->group(function () {
        Route::get('/', [RecommendationController::class, 'index'])->name('index');
        
        // Lab Requests
        Route::get('/lab-requests', [RecommendationController::class, 'labRequests'])->name('lab-requests');
        Route::post('/lab-requests', [RecommendationController::class, 'storeLabRequest'])->name('lab-requests.store');
        
        // Prescriptions
        Route::get('/prescriptions', [RecommendationController::class, 'prescriptions'])->name('prescriptions');
        Route::post('/prescriptions', [RecommendationController::class, 'storePrescription'])->name('prescriptions.store');
        
        // Diet Plans
        Route::get('/diet-plans', [RecommendationController::class, 'dietPlans'])->name('diet-plans');
        Route::post('/diet-plans', [RecommendationController::class, 'storeDietPlan'])->name('diet-plans.store');
        Route::get('/diet-plans/{dietPlan}/pdf', [RecommendationController::class, 'generateDietPlanPDF'])->name('diet-plans.pdf');
    });
    
    // Food Composition
    Route::prefix('food-composition')->name('food.')->group(function () {
        Route::get('/', [FoodCompositionController::class, 'index'])->name('index');
        Route::get('/search', [FoodCompositionController::class, 'search'])->name('search');
        Route::middleware('role:admin')->group(function () {
            Route::post('/', [FoodCompositionController::class, 'store'])->name('store');
            Route::put('/{food}', [FoodCompositionController::class, 'update'])->name('update');
            Route::delete('/{food}', [FoodCompositionController::class, 'destroy'])->name('destroy');
        });
    });
    
    // Finance Module
    Route::prefix('finance')->name('finance.')->middleware('role:admin,accountant')->group(function () {
        Route::get('/', [FinanceController::class, 'index'])->name('index');
        
        // Invoices
        Route::get('/invoices', [FinanceController::class, 'invoices'])->name('invoices');
        Route::post('/invoices', [FinanceController::class, 'storeInvoice'])->name('invoices.store');
        Route::get('/invoices/{invoice}/pdf', [FinanceController::class, 'generateInvoicePDF'])->name('invoices.pdf');
        
        // Expenses
        Route::get('/expenses', [FinanceController::class, 'expenses'])->name('expenses');
        Route::post('/expenses', [FinanceController::class, 'storeExpense'])->name('expenses.store');
        
        // Reports
        Route::get('/reports', [FinanceController::class, 'reports'])->name('reports');
        Route::get('/reports/cash-flow', [FinanceController::class, 'cashFlowReport'])->name('reports.cash-flow');
        Route::get('/reports/profit-loss', [FinanceController::class, 'profitLossReport'])->name('reports.profit-loss');
    });
    
    // Advertisements
    Route::prefix('advertisements')->name('advertisements.')->middleware('role:admin')->group(function () {
        Route::get('/', [AdvertisementController::class, 'index'])->name('index');
        Route::get('/create', [AdvertisementController::class, 'create'])->name('create');
        Route::post('/', [AdvertisementController::class, 'store'])->name('store');
        Route::get('/{advertisement}/edit', [AdvertisementController::class, 'edit'])->name('edit');
        Route::put('/{advertisement}', [AdvertisementController::class, 'update'])->name('update');
        Route::delete('/{advertisement}', [AdvertisementController::class, 'destroy'])->name('destroy');
    });
    
    // User Management
    Route::prefix('users')->name('users.')->middleware('role:admin,program_owner')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('index');
        Route::get('/create', [UserController::class, 'create'])->name('create');
        Route::post('/', [UserController::class, 'store'])->name('store');
        Route::get('/{user}/edit', [UserController::class, 'edit'])->name('edit');
        Route::put('/{user}', [UserController::class, 'update'])->name('update');
        Route::delete('/{user}', [UserController::class, 'destroy'])->name('destroy');
        
        // Activation codes
        Route::get('/activation-codes', [UserController::class, 'activationCodes'])->name('activation-codes');
        Route::post('/activation-codes', [UserController::class, 'generateActivationCode'])->name('activation-codes.generate');
    });
    
    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingsController::class, 'index'])->name('index');
        Route::post('/', [SettingsController::class, 'update'])->name('update');
        
        // Audit logs (Admin only)
        Route::get('/audit-logs', [SettingsController::class, 'auditLogs'])->name('audit-logs')->middleware('role:admin');
    });
});
